# Server Configuration
server.port=8080

# Database Configuration (H2 for development)
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=password

# H2 Console (for development only)
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

# MyBatis Plus Configuration
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
mybatis-plus.global-config.db-config.logic-delete-field=deleted
mybatis-plus.global-config.db-config.logic-delete-value=1
mybatis-plus.global-config.db-config.logic-not-delete-value=0
mybatis-plus.mapper-locations=classpath*:/mapper/**/*.xml

# CORS Configuration
spring.web.cors.allowed-origins=http://localhost:5173
spring.web.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
spring.web.cors.allowed-headers=*
spring.web.cors.allow-credentials=true

# MySQL Configuration (uncomment when ready to use MySQL)
# spring.datasource.url=*******************************************************************
# spring.datasource.username=root
# spring.datasource.password=yourpassword
# spring.jpa.database-platform=org.hibernate.dialect.MySQL8Dialect
# spring.jpa.hibernate.ddl-auto=update

import type { CSSProperties, Ref } from 'vue';
import type { TableV2Props } from '../table';
import type { AnyColumns, Column, KeyType } from '../types';
declare function useColumns(props: TableV2Props, columns: Ref<AnyColumns>, fixed: Ref<boolean>): {
    columns: import("vue").ComputedRef<{
        key: KeyType;
        align?: import("../types").Alignment;
        class?: string | import("../types").ClassNameGetter<any> | undefined;
        dataKey?: KeyType;
        fixed?: true | import("../types").FixedDirection;
        flexGrow?: CSSProperties["flexGrow"];
        flexShrink?: CSSProperties["flexShrink"];
        title?: string;
        hidden?: boolean;
        headerClass?: string | import("../types").HeaderClassGetter<any> | undefined;
        maxWidth?: number;
        minWidth?: number;
        style?: CSSProperties;
        sortable?: boolean;
        width: number;
        cellRenderer?: import("../types").CellRenderer<any> | undefined;
        headerCellRenderer?: import("../types").HeaderCellRenderer<any> | undefined;
    }[]>;
    columnsStyles: import("vue").ComputedRef<Record<KeyType, CSSProperties>>;
    columnsTotalWidth: import("vue").ComputedRef<number>;
    fixedColumnsOnLeft: import("vue").ComputedRef<{
        key: KeyType;
        align?: import("../types").Alignment;
        class?: string | import("../types").ClassNameGetter<any> | undefined;
        dataKey?: KeyType;
        fixed?: true | import("../types").FixedDirection;
        flexGrow?: CSSProperties["flexGrow"];
        flexShrink?: CSSProperties["flexShrink"];
        title?: string;
        hidden?: boolean;
        headerClass?: string | import("../types").HeaderClassGetter<any> | undefined;
        maxWidth?: number;
        minWidth?: number;
        style?: CSSProperties;
        sortable?: boolean;
        width: number;
        cellRenderer?: import("../types").CellRenderer<any> | undefined;
        headerCellRenderer?: import("../types").HeaderCellRenderer<any> | undefined;
    }[]>;
    fixedColumnsOnRight: import("vue").ComputedRef<{
        key: KeyType;
        align?: import("../types").Alignment;
        class?: string | import("../types").ClassNameGetter<any> | undefined;
        dataKey?: KeyType;
        fixed?: true | import("../types").FixedDirection;
        flexGrow?: CSSProperties["flexGrow"];
        flexShrink?: CSSProperties["flexShrink"];
        title?: string;
        hidden?: boolean;
        headerClass?: string | import("../types").HeaderClassGetter<any> | undefined;
        maxWidth?: number;
        minWidth?: number;
        style?: CSSProperties;
        sortable?: boolean;
        width: number;
        cellRenderer?: import("../types").CellRenderer<any> | undefined;
        headerCellRenderer?: import("../types").HeaderCellRenderer<any> | undefined;
    }[]>;
    hasFixedColumns: import("vue").ComputedRef<number>;
    mainColumns: import("vue").ComputedRef<AnyColumns>;
    normalColumns: import("vue").ComputedRef<{
        key: KeyType;
        align?: import("../types").Alignment;
        class?: string | import("../types").ClassNameGetter<any> | undefined;
        dataKey?: KeyType;
        fixed?: true | import("../types").FixedDirection;
        flexGrow?: CSSProperties["flexGrow"];
        flexShrink?: CSSProperties["flexShrink"];
        title?: string;
        hidden?: boolean;
        headerClass?: string | import("../types").HeaderClassGetter<any> | undefined;
        maxWidth?: number;
        minWidth?: number;
        style?: CSSProperties;
        sortable?: boolean;
        width: number;
        cellRenderer?: import("../types").CellRenderer<any> | undefined;
        headerCellRenderer?: import("../types").HeaderCellRenderer<any> | undefined;
    }[]>;
    visibleColumns: import("vue").ComputedRef<{
        key: KeyType;
        align?: import("../types").Alignment;
        class?: string | import("../types").ClassNameGetter<any> | undefined;
        dataKey?: KeyType;
        fixed?: true | import("../types").FixedDirection;
        flexGrow?: CSSProperties["flexGrow"];
        flexShrink?: CSSProperties["flexShrink"];
        title?: string;
        hidden?: boolean;
        headerClass?: string | import("../types").HeaderClassGetter<any> | undefined;
        maxWidth?: number;
        minWidth?: number;
        style?: CSSProperties;
        sortable?: boolean;
        width: number;
        cellRenderer?: import("../types").CellRenderer<any> | undefined;
        headerCellRenderer?: import("../types").HeaderCellRenderer<any> | undefined;
    }[]>;
    getColumn: (key: KeyType) => {
        key: KeyType;
        align?: import("../types").Alignment;
        class?: string | import("../types").ClassNameGetter<any> | undefined;
        dataKey?: KeyType;
        fixed?: true | import("../types").FixedDirection;
        flexGrow?: CSSProperties["flexGrow"];
        flexShrink?: CSSProperties["flexShrink"];
        title?: string;
        hidden?: boolean;
        headerClass?: string | import("../types").HeaderClassGetter<any> | undefined;
        maxWidth?: number;
        minWidth?: number;
        style?: CSSProperties;
        sortable?: boolean;
        width: number;
        cellRenderer?: import("../types").CellRenderer<any> | undefined;
        headerCellRenderer?: import("../types").HeaderCellRenderer<any> | undefined;
    } | undefined;
    getColumnStyle: (key: KeyType) => CSSProperties;
    updateColumnWidth: (column: Column<any>, width: number) => void;
    onColumnSorted: (e: MouseEvent) => void;
};
export { useColumns };
export type UseColumnsReturn = ReturnType<typeof useColumns>;

import type { TableV2Props } from '../table';
import type { KeyType } from '../types';
import type { UseRowReturn } from './use-row';
type UseDataProps = {
    expandedRowKeys: UseRowReturn['expandedRowKeys'];
    lastRenderedRowIndex: UseRowReturn['lastRenderedRowIndex'];
    resetAfterIndex: UseRowReturn['resetAfterIndex'];
};
export declare const useData: (props: TableV2Props, { expandedRowKeys, lastRenderedRowIndex, resetAfterIndex }: UseDataProps) => {
    data: import("vue").ComputedRef<any[]>;
    depthMap: import("vue").Ref<Record<KeyType, number>>;
};
export type UseDataReturn = ReturnType<typeof useData>;
export {};
